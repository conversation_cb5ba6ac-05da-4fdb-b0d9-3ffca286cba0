<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import {
  getOrders,
  createOrder,
  updateOrder,
  OrdersParams,
  getContractsSimpleList,
  changeOrder,
  createSubOrder,
  createRenewalOrder,
} from "../../services/income/order";
import type { OrderItem, OrderFormData } from "../../types/order";
import {
  formatDateTime,
  isoFormatDate,
  isoFormatDatetime,
} from "../../utils/common";
import { useToast } from "primevue/usetoast";
import { usePermission } from "../../composables/usePermission";
import {
  orderTypeOptions,
  orderPayTypeOptions,
  orderPayCycleOptions,
  orderServiceTypeOptions,
  orderClassOptions,
} from "../../utils/const";
import { getLevelStaticDataList } from "../../services/public";
import { optionLoaders } from "../../utils/options";
import type { LevelStaticDataInfo } from "../../types/public";
import OrderProcess from "./OrderProcess.vue";
import BillingProcess from "./BillingProcess.vue";
import OrderDetail from "./OrderDetail.vue";
import { useConfirm } from "primevue/useconfirm";
import { getFeePackagesSimpleList } from "../../services/feePackage";

const orders = ref<OrderItem[]>();
const loading = ref(false);
const totalRecords = ref(0);
const toast = useToast();
const confirm = useConfirm();

// 使用权限管理组合式函数
const { hasOperationPermission, initializeUserInfo } = usePermission();

// 新建/编辑订单相关状态
const orderDrawerVisible = ref(false);
const orderMode = ref<
  "create" | "edit" | "view" | "sub_order" | "modify_order" | "renewal_order"
>("create");
const editingOrder = ref<OrderItem | null>(null);

// 表单验证错误状态
const fieldErrors = ref<Record<string, string>>({});
const isSubmitting = ref(false);
const orderForm = ref<OrderFormData>({
  order_type: "",
  order_class: 0,
  order_contract_period: 0,
  order_start_year: new Date(),
  order_remark: "",
  business_product_type: "",
  sub_order_num: "",
  service_type: "",
  income_type: "",
  product_main_category: "",
  product_sub_category: "",
  product_scheme: "",
  product_after_sale: "",
  product_upload_file: "",
  pay_cycle: "",
  pay_type: "",
  account_seq: "",
  a_info: "",
  a_address: "",
  z_info: "",
  z_address: "",
  partner_name: "",
  partner_po_num: "",
  new_required_finished_date: new Date(),
  new_build_start_time: new Date(),
  new_build_finished_time: new Date(),
  finished_remark: "",
  finished_file: "",
  contract_num: "",
  customer_num: "",
  income_fee_package_id: 0,
  once_fee: 0,
  cycle_fee: 0,
  tax_rate: 0,
  tax_type: "",
  currency_type: "",
  charge_explain: "",
  charge_remark: "",
});

// 生成 A 到 Z 的字母数组
const letters = ref(
  Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i)) // A 到 Z
);

// 获取下一个子订单编号
const getNextSubOrderNum = (currentSubOrderNum: string): string => {
  if (!currentSubOrderNum) return "A";

  const currentCharCode = currentSubOrderNum.charCodeAt(0);
  if (currentCharCode >= 65 && currentCharCode < 90) {
    // A-Y
    return String.fromCharCode(currentCharCode + 1);
  } else if (currentCharCode === 90) {
    // Z
    return "AA"; // 如果是Z，可以考虑返回AA或者提示已达到最大值
  }
  return "A"; // 默认返回A
};

// 筛选相关
const selectedFilterColumn = ref("--");
const filterValue = ref("");
const filterContractNum = ref("");

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 20,
});

// 筛选选项
const filterColumnOptions = [
  { label: "--", value: "--" },
  { label: "创建用户", value: "create_user" },
  { label: "订单编号", value: "total_num" },
];

// 加载合同简单列表
const contractOptions = ref<
  { label: string; value: string; customer_num: string }[]
>([]);
const loadContractOptions = async () => {
  try {
    const response = await getContractsSimpleList();
    contractOptions.value = response.data.map((item) => ({
      label: `${item.contract_title} (${item.contract_num})`,
      value: item.contract_num,
      customer_num: item.main_customer_num,
    }));
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载合同列表失败",
      life: 3000,
    });
  }
};

// 加载费用套餐简单列表
const feePackageOptions = ref<{ label: string; value: number }[]>([]);
const loadFeePackageOptions = async () => {
  try {
    const response = await getFeePackagesSimpleList();
    feePackageOptions.value = response.data.map((item) => ({
      label: item.package_name,
      value: item.id,
    }));
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载费用套餐列表失败",
      life: 3000,
    });
  }
};

const loadOrders = async () => {
  try {
    loading.value = true;
    const params: OrdersParams = {
      page: lazyParams.value.page,
      pageSize: lazyParams.value.pageSize,
    };
    if (filterValue.value && selectedFilterColumn.value) {
      params.filter = filterValue.value;
      params.filterColumn = selectedFilterColumn.value;
    }
    if (filterContractNum.value) {
      params.contract_num = filterContractNum.value;
    }

    const response = await getOrders(params);
    orders.value = response.data.records;
    totalRecords.value = response.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "Error",
      detail: "加载订单列表失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 处理分页事件
const onPage = (event: { page: number; rows: number }) => {
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  loadOrders();
};

// 处理搜索
const handleSearch = () => {
  lazyParams.value.page = 1;
  loadOrders();
};

// 表单验证函数
const validateForm = (): boolean => {
  fieldErrors.value = {};
  let isValid = true;

  // 必填字段验证
  const requiredFields = [
    { key: "contract_num", label: "合同编号" },
    { key: "order_type", label: "订单类型" },
    { key: "order_class", label: "订单类别" },
    { key: "business_product_type", label: "业务产品类型" },
    { key: "sub_order_num", label: "子订单编号" },
    { key: "service_type", label: "服务类型" },
    { key: "income_type", label: "收入分类" },
    { key: "product_main_category", label: "产品主类" },
    { key: "product_sub_category", label: "产品子类" },
    { key: "pay_cycle", label: "付费周期" },
    { key: "pay_type", label: "付费方式" },
    { key: "tax_type", label: "税率类型" },
    { key: "currency_type", label: "货币类型" },
    { key: "income_fee_package_id", label: "费用套餐" },
  ];

  requiredFields.forEach((field) => {
    const value = orderForm.value[field.key as keyof OrderFormData];
    if (
      value === null ||
      value === undefined ||
      (typeof value === "string" && value.trim() === "") ||
      (typeof value === "number" && isNaN(value))
    ) {
      fieldErrors.value[field.key] = `${field.label}不能为空`;
      isValid = false;
    }
  });

  // 合约期验证
  if (orderForm.value.order_contract_period <= 0) {
    fieldErrors.value.order_contract_period = "合约期必须大于0";
    isValid = false;
  }

  return isValid;
};

// 清空表单错误
const clearFieldErrors = () => {
  fieldErrors.value = {};
};

// 打开新建订单
const openNew = async () => {
  orderMode.value = "create";
  orderForm.value = {
    order_type: "",
    order_class: 0,
    order_contract_period: 0,
    order_start_year: new Date(),
    order_remark: "",
    business_product_type: "",
    sub_order_num: "",
    service_type: "",
    income_type: "",
    product_main_category: "",
    product_sub_category: "",
    product_scheme: "",
    product_after_sale: "",
    product_upload_file: "",
    pay_cycle: "",
    pay_type: "",
    account_seq: "",
    a_info: "",
    a_address: "",
    z_info: "",
    z_address: "",
    partner_name: "",
    partner_po_num: "",
    new_required_finished_date: new Date(),
    new_build_start_time: new Date(),
    new_build_finished_time: new Date(),
    finished_remark: "",
    finished_file: "",
    contract_num: "",
    customer_num: "",
    income_fee_package_id: 0,
    once_fee: 0,
    cycle_fee: 0,
    tax_rate: 0,
    tax_type: "",
    currency_type: "",
    charge_explain: "",
    charge_remark: "",
  };

  // 清空产品次类选项和表单错误
  productSubCategoryOptions.value = [];
  clearFieldErrors();

  orderDrawerVisible.value = true;
};

// 编辑订单
const editOrder = async (order: OrderItem) => {
  editingOrder.value = order;

  // 创建一个深拷贝以避免直接修改原始对象
  const orderCopy = { ...order };

  // 处理日期字段 - 确保它们是Date对象
  if (orderCopy.new_build_start_time) {
    orderCopy.new_build_start_time = new Date(orderCopy.new_build_start_time);
  }
  if (orderCopy.new_build_finished_time) {
    orderCopy.new_build_finished_time = new Date(
      orderCopy.new_build_finished_time
    );
  }
  if (orderCopy.new_required_finished_date) {
    orderCopy.new_required_finished_date = new Date(
      orderCopy.new_required_finished_date
    );
  }
  if (orderCopy.order_start_year) {
    orderCopy.order_start_year = new Date(orderCopy.order_start_year);
  }

  orderForm.value = orderCopy;

  // 如果有产品主类，加载对应的产品次类选项（不清空当前值）
  if (order.product_main_category) {
    loadProductSubCategoryOptions(order.product_main_category);
  }

  // 清空表单错误
  clearFieldErrors();

  orderMode.value = "edit";
  orderDrawerVisible.value = true;
};

const handleDateFields = async (orderFormValue: OrderFormData) => {
  // 将iso格式的日期转换为字符串
  let dateFields = ["new_required_finished_date"];
  dateFields.forEach((field) => {
    (orderFormValue as any)[field] = orderFormValue[
      field as keyof typeof orderFormValue
    ]
      ? isoFormatDate(
          new Date(
            orderFormValue[field as keyof typeof orderFormValue] as
              | string
              | number
              | Date
          )
        )
      : "";
  });
  (orderFormValue as any)["order_start_year"] = orderFormValue.order_start_year
    ? String(new Date(orderFormValue.order_start_year).getFullYear())
    : "";
  let datetimeFields = ["new_build_start_time", "new_build_finished_time"];
  datetimeFields.forEach((field) => {
    (orderFormValue as any)[field] = orderFormValue[
      field as keyof typeof orderFormValue
    ]
      ? isoFormatDatetime(
          new Date(
            orderFormValue[field as keyof typeof orderFormValue] as
              | string
              | number
              | Date
          )
        )
      : "";
  });
};

// 保存订单
const saveOrder = async () => {
  // 前端表单验证
  if (!validateForm()) {
    toast.add({
      severity: "error",
      summary: "表单验证失败",
      detail: "请检查必填字段",
      life: 3000,
    });
    return;
  }

  isSubmitting.value = true;
  await handleDateFields(orderForm.value);

  try {
    if (orderMode.value === "create") {
      await createOrder(orderForm.value);
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "订单创建成功",
        life: 3000,
      });
    } else if (orderMode.value === "sub_order") {
      await createSubOrder(editingOrder.value!.id, orderForm.value);
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "子订单创建成功",
        life: 3000,
      });
    } else if (orderMode.value === "modify_order") {
      await changeOrder(editingOrder.value!.id, orderForm.value);
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "变更订单成功",
        life: 3000,
      });
    } else if (orderMode.value === "renewal_order") {
      await createRenewalOrder(editingOrder.value!.id, orderForm.value);
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "续约订单成功",
        life: 3000,
      });
    } else {
      await updateOrder(editingOrder.value!.id, orderForm.value);
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "订单更新成功",
        life: 3000,
      });
    }
    orderDrawerVisible.value = false;
    clearFieldErrors();
    loadOrders();
  } catch (error: any) {
    // 处理422验证错误
    if (error.response?.status === 422 && error.response.data.data.fields) {
      const fields = error.response.data.data.fields;
      // 清空旧错误
      fieldErrors.value = {};
      Object.keys(fields).forEach((key) => {
        fieldErrors.value[key] = fields[key]
          .map((item: any) => item.message)
          .join("; ");
      });
      toast.add({
        severity: "error",
        summary: "字段校验失败",
        detail: Object.values(fieldErrors.value).join("; "),
        life: 4000,
      });
    } else {
      toast.add({
        severity: "error",
        summary: "失败",
        detail: "保存订单失败",
        life: 3000,
      });
    }
  } finally {
    isSubmitting.value = false;
  }
};

// 查看订单详情
const viewOrderDetail = async (order: OrderItem) => {
  try {
    // 直接添加Tab页，不再保存订单详情到selectedOrder
    addTab("detail", order);
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "获取订单详情失败",
      life: 3000,
    });
  }
};

// 变更订单服务状态
const changeOrderStatus = (order: OrderItem) => {
  addTab("process", order);
};

// 打开计费审核
const openBillingAudit = (order: OrderItem) => {
  // 不可点击的服务状态
  const disabledStatuses = [
    "新装暂存",
    "新装下单审核",
    "新装派单",
    "拆机回退",
    "拆机下单审核",
    "拆机派单",
    "变更拆机回退",
    "变更拆机下单审核",
    "变更拆机派单",
  ];

  if (disabledStatuses.includes(order.service_status)) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "当前服务状态不支持计费审核操作",
      life: 3000,
    });
    return;
  }
  addTab("billing", order);
};

// 复制订单 - 通用方法
const copyOrder = async (
  order: OrderItem,
  action: "sub_order" | "modify_order" | "renewal_order"
) => {
  try {
    // 设置操作模式
    orderMode.value = action;
    editingOrder.value = order;

    // 复制订单数据
    orderForm.value = { ...order };

    // 根据操作类型设置服务类型默认值
    if (action === "sub_order") {
      orderForm.value.service_type = "新增";
    } else if (action === "modify_order") {
      orderForm.value.service_type = "变更";
    } else if (action === "renewal_order") {
      orderForm.value.service_type = "续约";
    }

    // 处理日期字段
    if (order.new_build_start_time) {
      orderForm.value.new_build_start_time = new Date(
        order.new_build_start_time
      );
    }
    if (order.new_build_finished_time) {
      orderForm.value.new_build_finished_time = new Date(
        order.new_build_finished_time
      );
    }
    if (order.new_required_finished_date) {
      orderForm.value.new_required_finished_date = new Date(
        order.new_required_finished_date
      );
    }
    if (order.order_start_year) {
      orderForm.value.order_start_year = new Date(order.order_start_year);
    }

    // 子订单才需要处理子订单编号
    if (action === "sub_order") {
      orderForm.value.sub_order_num = getNextSubOrderNum(
        order.sub_order_num || ""
      );
    }

    // 清空一些不应该复制的字段
    orderForm.value.order_remark = "";
    orderForm.value.finished_remark = "";
    orderForm.value.finished_file = "";

    // 如果有产品主类，加载对应的产品次类选项（不清空当前值）
    if (order.product_main_category) {
      loadProductSubCategoryOptions(order.product_main_category);
    }

    // 清空表单错误
    clearFieldErrors();

    orderDrawerVisible.value = true;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "复制订单数据失败",
      life: 3000,
    });
  }
};

// 复制订单 - 新建子订单
const copyAsSubOrder = (order: OrderItem) => copyOrder(order, "sub_order");

// 复制订单 - 变更订单
const copyAsModifyOrder = (order: OrderItem) =>
  copyOrder(order, "modify_order");

// 复制订单 - 续约订单
const copyAsRenewalOrder = (order: OrderItem) =>
  copyOrder(order, "renewal_order");

// 业务产品类型选项
const businessProductTypeOptions = ref<{ label: string; value: string }[]>([]);
const loadBusinessProductTypeOptions = () =>
  optionLoaders.businessProductType(businessProductTypeOptions);

// 收入分类选项
const incomeTypeOptions = ref<{ label: string; value: string }[]>([]);
const loadIncomeTypeOptions = () => optionLoaders.incomeType(incomeTypeOptions);

// 货币类型选项
const currencyOptions = ref<{ label: string; value: string }[]>([]);
const loadCurrencyOptions = () => optionLoaders.currencyType(currencyOptions);

// 税率类型选项
const taxRateOptions = ref<{ label: string; value: string }[]>([]);
const loadTaxTypeOptions = () => optionLoaders.taxRateType(taxRateOptions);

// 产品分类相关
const productCategories = ref<LevelStaticDataInfo[]>([]);
const productMainCategoryOptions = ref<{ label: string; value: string }[]>([]);
const productSubCategoryOptions = ref<{ label: string; value: string }[]>([]);

// 加载产品分类数据
const loadProductCategories = async () => {
  try {
    const response = await getLevelStaticDataList({ word: "product_category" });
    productCategories.value = response.data;

    // 生成产品主类选项
    productMainCategoryOptions.value = response.data.map((item) => ({
      label: item.data_value,
      value: item.data_value,
    }));
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载产品分类失败",
      life: 3000,
    });
  }
};

// 当产品主类改变时，更新产品次类选项
const onProductMainCategoryChange = (selectedMainCategory: string) => {
  // 清空产品次类的值
  orderForm.value.product_sub_category = "";

  // 加载产品次类选项
  loadProductSubCategoryOptions(selectedMainCategory);
};

// 当合同选择改变时，自动设置customer_num
const onContractChange = (selectedContractNum: string) => {
  // 找到选中的合同
  const selectedContract = contractOptions.value.find(
    (contract) => contract.value === selectedContractNum
  );

  // 如果找到合同，设置customer_num
  if (selectedContract) {
    orderForm.value.customer_num = selectedContract.customer_num;
  } else {
    orderForm.value.customer_num = "";
  }
  console.log(orderForm.value.customer_num);
};

// 加载产品次类选项（不清空当前值）
const loadProductSubCategoryOptions = (selectedMainCategory: string) => {
  // 找到选中的主类
  const selectedCategory = productCategories.value.find(
    (category) => category.data_value === selectedMainCategory
  );

  // 更新产品次类选项
  if (selectedCategory && selectedCategory.children) {
    productSubCategoryOptions.value = selectedCategory.children.map((item) => ({
      label: item.data_value,
      value: item.data_value,
    }));
  } else {
    productSubCategoryOptions.value = [];
  }
};

const activeTab = ref("list");
const tabs = ref<
  {
    id: string;
    title: string;
    type: "list" | "detail" | "process" | "billing";
    orderId?: number;
    totalNum?: string;
  }[]
>([{ id: "list", title: "订单列表", type: "list" }]);

// 添加新的 Tab 页
const addTab = (type: "detail" | "process" | "billing", order: OrderItem) => {
  // 查找是否存在同类型的标签页
  const existingTabIndex = tabs.value.findIndex((tab) => {
    return tab.id === `${type}-${order.id}`;
  });
  // 如果存在同类型标签页
  if (existingTabIndex > -1) {
    // 先将activeTab设为null，然后再设置为目标值，确保Vue能检测到变化
    const targetTabId = tabs.value[existingTabIndex].id;
    if (activeTab.value === targetTabId) {
      activeTab.value = "temp-force-update";
      // 使用nextTick确保DOM更新后再切换回目标Tab
      nextTick(() => {
        activeTab.value = targetTabId;
      });
    } else {
      activeTab.value = targetTabId;
    }
    return;
  }

  // 如果不存在，创建新标签页
  const newTab = {
    id: `${type}-${order.id}`,
    title:
      type === "detail"
        ? "订单详情"
        : type === "process"
        ? "服务审核"
        : "计费审核",
    type,
    orderId: order.id,
    totalNum: order.total_num,
  };
  tabs.value.push(newTab);
  activeTab.value = newTab.id;
};

// 关闭 Tab 页
const closeTab = (tabId: string) => {
  const index = tabs.value.findIndex((tab) => tab.id === tabId);
  if (index > -1) {
    tabs.value.splice(index, 1);
    // 如果关闭的是当前标签，切换到列表页
    if (activeTab.value === tabId) {
      activeTab.value = "list";
    } else {
      // 如果关闭的不是当前标签，切换到上一个标签
      activeTab.value = "temp-force-update";
      // 使用nextTick确保DOM更新后再切换回目标Tab
      nextTick(() => {
        activeTab.value = tabs.value[tabs.value.length - 1].id;
      });
    }
  }
};

onMounted(async () => {
  await initializeUserInfo();
  loadOrders();
  loadContractOptions();
  loadBusinessProductTypeOptions();
  loadIncomeTypeOptions();
  loadProductCategories();
  loadCurrencyOptions();
  loadTaxTypeOptions();
  loadFeePackageOptions();
});
</script>

<template>
  <div class="orders-container">
    <div class="card">
      <Tabs
        :value="activeTab"
        @update:modelValue="activeTab = $event"
        :scrollable="true"
        class="mb-2"
      >
        <TabList>
          <Tab
            v-for="tab in tabs.filter((t) => t.id !== 'temp-force-update')"
            :key="tab.id"
            :value="tab.id"
          >
            <span
              >{{ tab.title
              }}{{ tab.totalNum ? ` - ${tab.totalNum}` : "" }}</span
            >
            <Button
              v-if="tab.type !== 'list'"
              icon="pi pi-times"
              text
              rounded
              severity="danger"
              size="small"
              @click.stop="closeTab(tab.id)"
              class="close-button p-0"
            />
          </Tab>
        </TabList>
        <TabPanels>
          <TabPanel
            v-for="tab in tabs.filter((t) => t.id !== 'temp-force-update')"
            :key="tab.id"
            :value="tab.id"
          >
            <!-- 列表内容 -->
            <div v-if="tab.type === 'list'">
              <Toolbar class="mb-3">
                <template #start>
                  <div class="flex align-items-center gap-3">
                    <Select
                      v-model="filterContractNum"
                      :options="contractOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="请选择合同"
                      :showClear="true"
                      filter
                      @change="loadOrders"
                      style="min-width: 250px"
                    />
                  </div>
                </template>
                <template #end>
                  <div class="flex align-items-center gap-2">
                    <FloatLabel>
                      <Select
                        v-model="selectedFilterColumn"
                        :options="filterColumnOptions"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="请选择筛选字段"
                        style="width: 15rem"
                        size="normal"
                      />
                    </FloatLabel>
                    <FloatLabel>
                      <InputText v-model="filterValue" />
                      <label>搜索值</label>
                    </FloatLabel>
                    <Button
                      label="搜索"
                      icon="pi pi-search"
                      @click="handleSearch"
                      severity="secondary"
                      outlined
                    />
                    <Divider layout="vertical" />
                    <Button
                      label="新建订单"
                      icon="pi pi-plus"
                      :disabled="!hasOperationPermission"
                      @click="openNew"
                      severity="success"
                      class="apple-primary-button"
                    />
                  </div>
                </template>
              </Toolbar>

              <DataTable
                :value="orders"
                :paginator="true"
                :lazy="true"
                :loading="loading"
                :rows="20"
                :rowsPerPageOptions="[10, 20, 50]"
                :totalRecords="totalRecords"
                @page="onPage($event)"
                showGridlines
                stripedRows
                scrollable
                scrollHeight="calc(100vh - 28rem)"
              >
                <template #empty>
                  <div class="empty-message">
                    <i
                      class="pi pi-inbox"
                      style="
                        font-size: 2rem;
                        color: var(--p-text-color-secondary);
                        margin-bottom: 1rem;
                      "
                    ></i>
                    <p>暂无订单数据</p>
                  </div>
                </template>
                <Column
                  field="total_num"
                  header="订单编号"
                  style="min-width: 17rem"
                />
                <Column
                  field="contract_title"
                  header="合同标题"
                  style="min-width: 21rem"
                />
                <Column
                  field="create_user"
                  header="创建用户"
                  style="min-width: 7rem"
                />
                <Column
                  field="order_type"
                  header="服务类型"
                  style="min-width: 7rem"
                />
                <Column
                  field="pay_type"
                  header="付费方式"
                  style="min-width: 7rem"
                />
                <Column
                  field="once_fee"
                  header="一次性费用"
                  style="min-width: 8rem"
                >
                  <template #body="slotProps">
                    {{ slotProps.data.once_fee || "--" }}
                  </template>
                </Column>
                <Column
                  field="cycle_fee"
                  header="周期性费用"
                  style="min-width: 8rem"
                >
                  <template #body="slotProps">
                    {{ slotProps.data.cycle_fee || "--" }}
                  </template>
                </Column>
                <Column
                  field="currency_type"
                  header="货币类型"
                  style="min-width: 7rem"
                />
                <Column
                  field="service_status"
                  header="服务状态"
                  style="min-width: 10rem"
                />
                <Column
                  field="bill_status"
                  header="账务状态"
                  style="min-width: 10rem"
                />
                <Column
                  field="created_at"
                  header="创建时间"
                  style="min-width: 12.5rem"
                >
                  <template #body="slotProps">
                    {{ formatDateTime(slotProps.data.created_at) }}
                  </template>
                </Column>
                <Column
                  header="操作"
                  alignFrozen="right"
                  frozen
                  style="white-space: nowrap"
                >
                  <template #body="slotProps">
                    <Button
                      icon="pi pi-pencil"
                      outlined
                      rounded
                      :disabled="
                        !hasOperationPermission ||
                        slotProps.data.service_status !== '新装暂存'
                      "
                      @click="editOrder(slotProps.data)"
                      v-tooltip.top="
                        slotProps.data.service_status === '新装暂存'
                          ? '编辑订单信息'
                          : '新装暂存状态可以编辑'
                      "
                      class="mr-2"
                    />
                    <Button
                      icon="pi pi-eye"
                      outlined
                      rounded
                      severity="info"
                      @click="viewOrderDetail(slotProps.data)"
                      v-tooltip.top="'查看订单详情'"
                      class="mr-2"
                    />
                    <Button
                      icon="pi pi-sync pi-spin"
                      outlined
                      rounded
                      severity="help"
                      @click="changeOrderStatus(slotProps.data)"
                      v-tooltip.top="'变更服务状态'"
                      class="mr-2"
                    />
                    <Button
                      icon="pi pi-dollar"
                      outlined
                      rounded
                      severity="danger"
                      class="mr-2"
                      @click="openBillingAudit(slotProps.data)"
                      :disabled="
                        [
                          '新装暂存',
                          '新装下单审核',
                          '新装派单',
                          '拆机回退',
                          '拆机下单审核',
                          '拆机派单',
                          '变更拆机回退',
                          '变更拆机下单审核',
                          '变更拆机派单',
                        ].includes(slotProps.data.service_status)
                      "
                      v-tooltip.top="
                        [
                          '新装暂存',
                          '新装下单审核',
                          '新装派单',
                          '拆机回退',
                          '拆机下单审核',
                          '拆机派单',
                          '变更拆机回退',
                          '变更拆机下单审核',
                          '变更拆机派单',
                        ].includes(slotProps.data.service_status)
                          ? '当前服务状态不支持计费审核操作'
                          : '计费审核'
                      "
                    />
                    <SplitButton
                      icon="pi pi-copy"
                      :model="[
                        {
                          label: '新建子订单',
                          icon: 'pi pi-copy',
                          command: () => copyAsSubOrder(slotProps.data),
                        },
                        {
                          label: '变更订单',
                          icon: 'pi pi-sync',
                          command: () => copyAsModifyOrder(slotProps.data),
                        },
                        {
                          label: '续约订单',
                          icon: 'fa fa-light fa-pen-to-square',
                          command: () => copyAsRenewalOrder(slotProps.data),
                        },
                      ]"
                      @click="copyAsSubOrder(slotProps.data)"
                      severity="secondary"
                      outlined
                      rounded
                      size="normal"
                      v-tooltip.top="'新建(子)/变更/续约订单'"
                    />
                  </template>
                </Column>
              </DataTable>
            </div>

            <!-- 订单详情内容 -->
            <div
              v-if="tab.type === 'detail' && tab.orderId"
              class="order-detail"
            >
              <OrderDetail :orderId="tab.orderId" />
            </div>

            <!-- 服务审核内容 -->
            <div
              v-if="tab.type === 'process' && tab.orderId"
              class="order-process"
            >
              <OrderProcess
                :orderId="tab.orderId"
                :confirm="confirm"
                :dialogKey="`process-${tab.orderId}`"
              />
            </div>

            <!-- 计费审核内容 -->
            <div
              v-if="tab.type === 'billing' && tab.orderId"
              class="order-billing"
            >
              <BillingProcess
                :orderId="tab.orderId"
                :confirm="confirm"
                :dialogKey="`billing-${tab.orderId}`"
              />
            </div>
          </TabPanel>
        </TabPanels>
      </Tabs>

      <!-- 新建/编辑订单/变更订单/新建子订单 抽屉 -->
      <Drawer
        v-model:visible="orderDrawerVisible"
        position="right"
        :style="{ width: '80rem' }"
        :modal="true"
        :closable="true"
        :dismissable="false"
        :showCloseIcon="true"
        :header="
          orderMode === 'create'
            ? '新建订单信息'
            : orderMode === 'sub_order'
            ? '新建子订单'
            : orderMode === 'modify_order'
            ? '变更订单'
            : orderMode === 'renewal_order'
            ? '续约订单'
            : '编辑订单信息'
        "
        class="order-drawer p-fluid"
      >
        <div class="p-4">
          <!-- 基本信息 -->
          <div class="form-section">
            <div class="section-header">
              <h3 class="section-title">基本信息</h3>
              <Divider />
            </div>
            <div class="section-content">
              <Fluid>
                <div class="grid grid-cols-3 gap-4">
                  <!-- 前序订单编号 - 仅在子订单/变更订单/续约订单时显示 -->
                  <div
                    v-if="
                      orderMode === 'sub_order' ||
                      orderMode === 'modify_order' ||
                      orderMode === 'renewal_order'
                    "
                    class="field"
                  >
                    <label for="parent_order_num">前序订单编号</label>
                    <InputText
                      id="parent_order_num"
                      :value="editingOrder?.total_num || ''"
                      readonly
                      disabled
                      placeholder="前序订单编号"
                      class="bg-surface-100 dark:bg-surface-800"
                    />
                  </div>
                  <div class="field">
                    <label for="contract_num" class="required">合同编号</label>
                    <Select
                      v-model="orderForm.contract_num"
                      :options="contractOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择关联合同"
                      filter
                      :class="[{ 'p-invalid': fieldErrors.contract_num }]"
                      @change="onContractChange($event.value)"
                    />
                    <small v-if="fieldErrors.contract_num" class="p-error">
                      {{ fieldErrors.contract_num }}
                    </small>
                  </div>
                  <div class="field">
                    <label for="order_type" class="required">订单类型</label>
                    <Select
                      v-model="orderForm.order_type"
                      :options="orderTypeOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择订单类型"
                      :class="[{ 'p-invalid': fieldErrors.order_type }]"
                    />
                    <small v-if="fieldErrors.order_type" class="p-error">
                      {{ fieldErrors.order_type }}
                    </small>
                  </div>
                  <div class="field">
                    <label for="order_class" class="required">订单类别</label>
                    <Select
                      v-model="orderForm.order_class"
                      :options="orderClassOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择订单类别"
                      :class="[{ 'p-invalid': fieldErrors.order_class }]"
                    />
                    <small v-if="fieldErrors.order_class" class="p-error">
                      {{ fieldErrors.order_class }}
                    </small>
                  </div>
                  <div class="field">
                    <label for="business_product_type" class="required"
                      >业务产品类型</label
                    >
                    <Select
                      v-model="orderForm.business_product_type"
                      :options="businessProductTypeOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择业务产品类型"
                      :class="[
                        { 'p-invalid': fieldErrors.business_product_type },
                      ]"
                    />
                    <small
                      v-if="fieldErrors.business_product_type"
                      class="p-error"
                    >
                      {{ fieldErrors.business_product_type }}
                    </small>
                  </div>
                  <div class="field">
                    <label for="sub_order_num" class="required"
                      >子订单编号</label
                    >
                    <Select
                      v-model="orderForm.sub_order_num"
                      :options="letters"
                      placeholder="选择一个子编号"
                      :disabled="
                        orderMode === 'sub_order' ||
                        orderMode === 'modify_order'
                      "
                      filter
                      :class="[{ 'p-invalid': fieldErrors.sub_order_num }]"
                    />
                    <small v-if="fieldErrors.sub_order_num" class="p-error">
                      {{ fieldErrors.sub_order_num }}
                    </small>
                  </div>
                  <div class="field">
                    <label for="order_start_year" class="required"
                      >订单开始年份</label
                    >
                    <DatePicker
                      v-model="orderForm.order_start_year"
                      view="year"
                      dateFormat="yy"
                      showIcon
                      :class="[{ 'p-invalid': fieldErrors.order_start_year }]"
                    />
                    <small v-if="fieldErrors.order_start_year" class="p-error">
                      {{ fieldErrors.order_start_year }}
                    </small>
                  </div>
                  <div class="field">
                    <label for="order_contract_period" class="required"
                      >合约期（月数）</label
                    >
                    <InputNumber
                      v-model="orderForm.order_contract_period"
                      :min="0"
                      showButtons
                      :class="[
                        { 'p-invalid': fieldErrors.order_contract_period },
                      ]"
                    />
                    <small
                      v-if="fieldErrors.order_contract_period"
                      class="p-error"
                    >
                      {{ fieldErrors.order_contract_period }}
                    </small>
                  </div>
                  <div class="field">
                    <label for="order_remark_basic">订单备注</label>
                    <Textarea v-model="orderForm.order_remark" autoResize />
                  </div>
                </div>
              </Fluid>
            </div>
          </div>

          <!-- 产品信息 -->
          <div class="form-section">
            <div class="section-header">
              <h3 class="section-title">产品信息</h3>
              <Divider />
            </div>
            <div class="section-content">
              <Fluid>
                <div class="grid grid-cols-3 gap-4">
                  <div class="field">
                    <label for="service_type" class="required">服务类型</label>
                    <Select
                      v-model="orderForm.service_type"
                      :options="orderServiceTypeOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择服务类型"
                      :disabled="
                        orderMode === 'sub_order' ||
                        orderMode === 'modify_order' ||
                        orderMode === 'renewal_order'
                      "
                      :class="[{ 'p-invalid': fieldErrors.service_type }]"
                    />
                    <small v-if="fieldErrors.service_type" class="p-error">
                      {{ fieldErrors.service_type }}
                    </small>
                  </div>
                  <div class="field">
                    <label for="income_type" class="required">收入分类</label>
                    <Select
                      v-model="orderForm.income_type"
                      :options="incomeTypeOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="请选择收入分类"
                      :class="[{ 'p-invalid': fieldErrors.income_type }]"
                    />
                    <small v-if="fieldErrors.income_type" class="p-error">
                      {{ fieldErrors.income_type }}
                    </small>
                  </div>
                  <div class="field">
                    <label for="product_main_category" class="required"
                      >产品主类</label
                    >
                    <Select
                      v-model="orderForm.product_main_category"
                      :options="productMainCategoryOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="请选择产品主类"
                      :class="[
                        { 'p-invalid': fieldErrors.product_main_category },
                      ]"
                      @change="onProductMainCategoryChange($event.value)"
                    />
                    <small
                      v-if="fieldErrors.product_main_category"
                      class="p-error"
                    >
                      {{ fieldErrors.product_main_category }}
                    </small>
                  </div>
                  <div class="field">
                    <label for="product_sub_category" class="required"
                      >产品子类</label
                    >
                    <Select
                      v-model="orderForm.product_sub_category"
                      :options="productSubCategoryOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="请先选择产品主类"
                      :disabled="!orderForm.product_main_category"
                      :class="[
                        { 'p-invalid': fieldErrors.product_sub_category },
                      ]"
                    />
                    <small
                      v-if="fieldErrors.product_sub_category"
                      class="p-error"
                    >
                      {{ fieldErrors.product_sub_category }}
                    </small>
                  </div>
                  <div class="field">
                    <label for="product_scheme">产品方案</label>
                    <InputText v-model="orderForm.product_scheme" />
                  </div>
                  <div class="field">
                    <label for="product_after_sale">产品售后</label>
                    <InputText v-model="orderForm.product_after_sale" />
                  </div>
                  <div class="field">
                    <label for="product_upload_file">产品上传文件</label>
                    <InputText v-model="orderForm.product_upload_file" />
                  </div>
                </div>
              </Fluid>
            </div>
          </div>

          <!-- 付费信息 -->
          <div class="form-section">
            <div class="section-header">
              <h3 class="section-title">付费信息</h3>
              <Divider />
            </div>
            <div class="section-content">
              <Fluid>
                <div class="grid grid-cols-3 gap-4">
                  <div class="field">
                    <label for="pay_cycle" class="required">付费周期</label>
                    <Select
                      v-model="orderForm.pay_cycle"
                      :options="orderPayCycleOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择付费周期"
                      :class="[{ 'p-invalid': fieldErrors.pay_cycle }]"
                    />
                    <small v-if="fieldErrors.pay_cycle" class="p-error">
                      {{ fieldErrors.pay_cycle }}
                    </small>
                  </div>
                  <div class="field">
                    <label for="pay_type" class="required">付费方式</label>
                    <Select
                      v-model="orderForm.pay_type"
                      :options="orderPayTypeOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择付费方式"
                      :class="[{ 'p-invalid': fieldErrors.pay_type }]"
                    />
                    <small v-if="fieldErrors.pay_type" class="p-error">
                      {{ fieldErrors.pay_type }}
                    </small>
                  </div>
                  <div class="field">
                    <label for="income_fee_package_id" class="required"
                      >费用套餐</label
                    >
                    <Select
                      v-model="orderForm.income_fee_package_id"
                      :options="feePackageOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择费用套餐"
                      :class="[
                        { 'p-invalid': fieldErrors.income_fee_package_id },
                      ]"
                    />
                    <small
                      v-if="fieldErrors.income_fee_package_id"
                      class="p-error"
                    >
                      {{ fieldErrors.income_fee_package_id }}
                    </small>
                  </div>
                  <div class="field">
                    <label for="once_fee">一次性费用</label>
                    <InputNumber
                      v-model="orderForm.once_fee"
                      showButtons
                      mode="currency"
                      :currency="orderForm.currency_type || 'CNY'"
                      placeholder="输入一次性费用"
                      :class="[{ 'p-invalid': fieldErrors.once_fee }]"
                    />
                    <small v-if="fieldErrors.once_fee" class="p-error">
                      {{ fieldErrors.once_fee }}
                    </small>
                  </div>
                  <div class="field">
                    <label for="cycle_fee">周期性费用</label>
                    <InputNumber
                      v-model="orderForm.cycle_fee"
                      showButtons
                      mode="currency"
                      :currency="orderForm.currency_type || 'CNY'"
                      placeholder="输入周期性费用"
                      :class="[{ 'p-invalid': fieldErrors.cycle_fee }]"
                    />
                    <small v-if="fieldErrors.cycle_fee" class="p-error">
                      {{ fieldErrors.cycle_fee }}
                    </small>
                  </div>
                  <div class="field">
                    <label for="tax_rate">税率</label>
                    <InputNumber
                      v-model="orderForm.tax_rate"
                      :min="0"
                      showButtons
                      placeholder="输入税率"
                      :class="[{ 'p-invalid': fieldErrors.tax_rate }]"
                    />
                    <small v-if="fieldErrors.tax_rate" class="p-error">
                      {{ fieldErrors.tax_rate }}
                    </small>
                  </div>
                  <div class="field">
                    <label for="tax_type" class="required">税率类型</label>
                    <Select
                      v-model="orderForm.tax_type"
                      :options="taxRateOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择税率类型"
                      :class="[{ 'p-invalid': fieldErrors.tax_type }]"
                    />
                    <small v-if="fieldErrors.tax_type" class="p-error">
                      {{ fieldErrors.tax_type }}
                    </small>
                  </div>
                  <div class="field">
                    <label for="currency_type" class="required">货币类型</label>
                    <Select
                      v-model="orderForm.currency_type"
                      :options="currencyOptions"
                      optionLabel="label"
                      optionValue="value"
                      placeholder="选择货币类型"
                      :class="[{ 'p-invalid': fieldErrors.currency_type }]"
                    />
                    <small v-if="fieldErrors.currency_type" class="p-error">
                      {{ fieldErrors.currency_type }}
                    </small>
                  </div>
                  <div class="field">
                    <label for="charge_explain">资费信息</label>
                    <InputText v-model="orderForm.charge_explain" />
                  </div>
                  <div class="field col-span-3">
                    <label for="charge_remark">资费备注</label>
                    <Textarea v-model="orderForm.charge_remark" autoResize />
                  </div>
                </div>
              </Fluid>
            </div>
          </div>

          <!-- 端点信息 -->
          <div class="form-section">
            <div class="section-header">
              <h3 class="section-title">端点信息</h3>
              <Divider />
            </div>
            <div class="section-content">
              <Fluid>
                <div class="grid grid-cols-2 gap-4">
                  <div class="field">
                    <label for="a_info">A端信息</label>
                    <InputText v-model="orderForm.a_info" />
                  </div>
                  <div class="field">
                    <label for="a_address">A端地址</label>
                    <InputText v-model="orderForm.a_address" />
                  </div>
                  <div class="field">
                    <label for="z_info">Z端信息</label>
                    <InputText v-model="orderForm.z_info" />
                  </div>
                  <div class="field">
                    <label for="z_address">Z端地址</label>
                    <InputText v-model="orderForm.z_address" />
                  </div>
                </div>
              </Fluid>
            </div>
          </div>

          <!-- 合作商信息 -->
          <div class="form-section">
            <div class="section-header">
              <h3 class="section-title">合作商信息</h3>
              <Divider />
            </div>
            <div class="section-content">
              <Fluid>
                <div class="grid grid-cols-2 gap-4">
                  <div class="field">
                    <label for="partner_name">合作商名称</label>
                    <InputText v-model="orderForm.partner_name" />
                  </div>
                  <div class="field">
                    <label for="partner_po_num">合作商编号</label>
                    <InputText v-model="orderForm.partner_po_num" />
                  </div>
                </div>
              </Fluid>
            </div>
          </div>

          <!-- 新装信息 -->
          <div class="form-section">
            <div class="section-header">
              <h3 class="section-title">新装信息</h3>
              <Divider />
            </div>
            <div class="section-content">
              <Fluid>
                <div class="grid grid-cols-3 gap-4">
                  <div class="field">
                    <label for="new_required_finished_date"
                      >新装要求完工日</label
                    >
                    <DatePicker
                      v-model="orderForm.new_required_finished_date"
                      dateFormat="yy-mm-dd"
                      showIcon
                      :manualInput="false"
                    />
                  </div>
                  <div class="field">
                    <label for="new_build_start_time">实际开始实施时间</label>
                    <DatePicker
                      v-model="orderForm.new_build_start_time"
                      dateFormat="yy-mm-dd"
                      showIcon
                      showTime
                      hourFormat="24"
                    />
                  </div>
                  <div class="field">
                    <label for="new_build_finished_time">实施报完工时间</label>
                    <DatePicker
                      v-model="orderForm.new_build_finished_time"
                      dateFormat="yy-mm-dd"
                      showIcon
                      showTime
                      hourFormat="24"
                    />
                  </div>
                </div>
              </Fluid>
            </div>
          </div>

          <!-- 完工信息 -->
          <div class="form-section">
            <div class="section-header">
              <h3 class="section-title">完工信息</h3>
              <Divider />
            </div>
            <div class="section-content">
              <Fluid>
                <div class="grid grid-cols-1 gap-4">
                  <div class="field">
                    <label for="finished_remark">完工备注</label>
                    <Textarea v-model="orderForm.finished_remark" autoResize />
                  </div>
                  <div class="field">
                    <label for="finished_file">完工文件</label>
                    <InputText v-model="orderForm.finished_file" />
                  </div>
                </div>
              </Fluid>
            </div>
          </div>
        </div>
        <template #footer>
          <div class="flex justify-end gap-2">
            <Button
              label="取消"
              icon="pi pi-times"
              severity="secondary"
              outlined
              @click="orderDrawerVisible = false"
            />
            <Button
              label="保存"
              icon="pi pi-check"
              @click="saveOrder"
              severity="success"
              :loading="isSubmitting"
              :disabled="isSubmitting"
            />
          </div>
        </template>
      </Drawer>
    </div>
  </div>
</template>

<style scoped>
.orders-container {
  padding: 1rem;
  height: calc(100vh - 15rem);
}

.card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 1px 3px 0 rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 1rem;
}

.form-section {
  margin-bottom: 2rem;
  border-radius: 12px;
  border: 1px solid var(--surface-border);
  overflow: hidden;
}

.section-header {
  padding: 1.5rem 1.5rem 0 1.5rem;
  background: var(--p-surface-card);
}

.section-content {
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.field {
  margin-bottom: 0.5rem;
}

.field label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--p-primary-color);
  letter-spacing: -0.025em;
}

.field label.required::after {
  content: " *";
  color: #ff3b30;
  font-weight: 600;
  margin-left: 2px;
}

.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: var(--surface-ground);
  border-radius: 6px;
}

.empty-message p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
}

.section-title {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  letter-spacing: -0.025em;
}

:deep(.p-tabview) {
  background: transparent;
}

:deep(.p-tabview-nav) {
  border: none;
  background: transparent;
  margin-bottom: 1rem;
}

:deep(.p-tabview-nav-link) {
  background: var(--surface-ground);
  border: 1px solid var(--surface-border);
  border-radius: 6px;
  margin-right: 0.5rem;
  padding: 0.75rem 1rem;
  transition: all 0.2s;
  user-select: none;
}

:deep(.p-tabview-selected .p-tabview-nav-link) {
  background: var(--surface-card);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

:deep(.p-tabview-nav-link:not(.p-disabled):focus) {
  box-shadow: none;
  border-color: var(--primary-color);
}

:deep(.p-tabview-panels) {
  background: var(--surface-card);
  border-radius: 6px;
  padding: 1.5rem;
}

.close-button {
  opacity: 0.7;
  transition: opacity 0.2s;
}

.close-button:hover {
  opacity: 1;
}

/* SplitButton 样式 */
:deep(.p-splitbutton-button) {
  /* 主按钮样式 */
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  color: var(--primary-color) !important;
  font-weight: 500 !important;
  font-size: 0.875rem !important;
  padding: 0.5rem 0.75rem !important;
  border-radius: 20px 0 0 20px !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

:deep(.p-splitbutton-button:hover) {
  background: rgba(var(--primary-color-rgb), 0.1) !important;
  border-color: var(--primary-color) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

:deep(.p-splitbutton-dropdown) {
  /* 主按钮样式 */
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  color: var(--primary-color) !important;
  font-weight: 500 !important;
  font-size: 0.875rem !important;
  padding: 0.5rem 0.75rem !important;
  border-radius: 0 20px 20px 0 !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

:deep(.p-splitbutton-dropdown:hover) {
  background: rgba(var(--primary-color-rgb), 0.1) !important;
  border-color: var(--primary-color) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

:deep(.p-datatable .p-datatable-thead > tr > th) {
  background: #f8f9fa !important;
  border-bottom: 2px solid #e9ecef !important;
}

:deep(.p-datatable .p-datatable-tbody > tr > td) {
  border-bottom: 1px solid #f1f1f1 !important;
  font-size: 0.9rem !important;
  color: #1d1d1f !important;
  padding: 0.5rem 1rem !important;
}

/* 订单抽屉样式 - 参考ContractList.vue */
:deep(.order-drawer) {
  .p-drawer-content {
    display: flex;
    flex-direction: column;
  }
}

/* 表单验证错误样式 */
.p-error {
  color: #ff3b30;
  font-size: 0.75rem;
  font-weight: 400;
  margin-top: 0.25rem;
  display: block;
  line-height: 1.2;
}

:deep(.p-invalid) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 1px #ff3b30 !important;
}

:deep(.p-invalid:focus) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 3px rgba(255, 59, 48, 0.1) !important;
}

/* 提交按钮加载状态 */
:deep(.p-button:disabled) {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }

  .grid-cols-3 {
    grid-template-columns: 1fr;
  }

  .section-header,
  .section-content {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .p-error {
    font-size: 0.7rem;
  }
}
</style>
